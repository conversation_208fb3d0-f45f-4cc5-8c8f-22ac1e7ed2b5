import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { CircleIcon, InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import type { ProgressBarActiveColor } from '@shape-construction/arch-ui/src/ProgressBar/ProgressBar';
import classNames from 'clsx';

// DetailsListHeader Component
export type DetailsListHeaderProps = {
  title: string;
  progressPercentagesDescription?: string; // Example: "10/20%"
  isComplete?: boolean;
  isDeactivated?: boolean;
};

export const DetailsListHeader: React.FC<DetailsListHeaderProps> = ({
  title,
  progressPercentagesDescription,
  isComplete,
  isDeactivated,
}) => {
  return (
    <div className="flex pt-3 pb-1 justify-between items-center">
      <div className="text-xs leading-4 font-semibold tracking-wider uppercase text-gray-400">{title}</div>
      {progressPercentagesDescription && (
        <div
          className={classNames('text-xs leading-4 font-medium', {
            'opacity-50': isDeactivated,
            'text-gray-400': !isComplete,
            'text-green-500': isComplete && !isDeactivated,
            'text-gray-700': isComplete && isDeactivated,
          })}
        >
          {progressPercentagesDescription}
        </div>
      )}
    </div>
  );
};

// DetailsListText Component
type CheckedRowProps = { isChecked: boolean; isDeactivated?: boolean };

const CheckedRow: React.FC<CheckedRowProps> = ({ isChecked, isDeactivated }) =>
  isChecked ? (
    <CheckCircleIcon
      aria-label="checked progress item"
      className={classNames('w-5 h-5', {
        'text-green-500': !isDeactivated,
        'text-gray-500': isDeactivated,
      })}
    />
  ) : (
    <CircleIcon aria-label="unchecked progress item" className="w-5 h-5 text-gray-400" />
  );

export type DetailsListTextProps = {
  text: string;
  isComplete?: boolean;
  isDeactivated?: boolean;
};

export const DetailsListText: React.FC<DetailsListTextProps> = ({ text, isComplete, isDeactivated }) => {
  return (
    <div
      className={classNames('flex py-2 justify-between items-center', {
        'opacity-50': isDeactivated,
      })}
    >
      <div
        className={classNames('text-sm leading-5 font-medium text-gray-700', {
          'opacity-50': isComplete,
        })}
      >
        {text}
      </div>
      {isComplete !== undefined && <CheckedRow isChecked={isComplete} isDeactivated={isDeactivated} />}
    </div>
  );
};

// DetailsListProgressBar Component
export type DetailsListProgressBarProps = {
  currentProgress: number;
  description: string;
  isComplete?: boolean;
  isDeactivated?: boolean;
};

const progressBarColor = (isComplete: boolean, isDeactivated: boolean): ProgressBarActiveColor => {
  if (isDeactivated) return 'secondary';
  if (!isComplete) return 'primary';

  return 'success';
};

export const DetailsListProgressBar: React.FC<DetailsListProgressBarProps> = ({
  currentProgress,
  description,
  isComplete,
  isDeactivated,
}) => {
  const color: ProgressBarActiveColor = progressBarColor(!!isComplete, !!isDeactivated);

  return (
    <div className="pt-1 pb-2">
      <ProgressBar.Root progress={currentProgress} color={color} disabled={isDeactivated} />
    </div>
  );
};

// Main IssueQualityDetails Component
export const IssueQualityDetails: React.FC = () => {
  const messages = useMessageGetter('dataBook.page.issueTracker.qualityDetails');

  return (
    <div className="px-4 py-3">
      <div className="w-full lg:w-72">
        <div className="flex py-2 justify-between items-center">
          <div className="text-sm leading-5 font-medium text-gray-700">{messages('currentScore')}</div>
          <Badge label="Good" theme="blue" />
        </div>
      </div>
      <Divider orientation="horizontal" />

      {/* Completeness Section */}
      <div>
        <DetailsListHeader
          title={messages('completeness.title')}
          progressPercentagesDescription={messages('completeness.titleInfo', { complete: 3, total: 4 })}
          isComplete={false}
        />
        <DetailsListText text={messages('completeness.titleProvided')} isComplete={true} />
        <DetailsListText text={messages('completeness.descriptionAdded')} isComplete={true} />
        <DetailsListText text={messages('completeness.locationSpecified')} isComplete={true} />
        <DetailsListText text={messages('completeness.categoryAssigned')} isComplete={false} />
      </div>

      <Divider orientation="horizontal" />

      {/* Activity Section */}
      <div>
        <DetailsListHeader
          title={messages('activity.title')}
          progressPercentagesDescription={messages('activity.titleInfo', { complete: 2, total: 3 })}
          isComplete={false}
        />
        <DetailsListText text={messages('activity.commentsAdded')} isComplete={true} />
        <DetailsListText text={messages('activity.statusUpdates')} isComplete={true} />
        <DetailsListText text={messages('activity.recentActivity')} isComplete={false} />
        <DetailsListProgressBar
          currentProgress={67}
          isComplete={false}
          description="Activity level: 67%"
        />
      </div>

      <Divider orientation="horizontal" />

      {/* Evidence Section */}
      <div>
        <DetailsListHeader
          title={messages('evidence.title')}
          progressPercentagesDescription={messages('evidence.titleInfo', { complete: 1, total: 2 })}
          isComplete={false}
        />
        <DetailsListText text={messages('evidence.photosAttached')} isComplete={true} />
        <DetailsListText text={messages('evidence.documentsUploaded')} isComplete={false} />
        <DetailsListProgressBar
          currentProgress={50}
          isComplete={false}
          description="Evidence completeness: 50%"
        />
      </div>

      <div className="flex py-2 space-x-1">
        <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
        <p className="text-xs leading-4 font-normal text-blue-700">
          {messages('info.description')}
        </p>
      </div>
    </div>
  );
};
